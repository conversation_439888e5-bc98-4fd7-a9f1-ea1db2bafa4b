/**
 * SEO Configuration for DocuChampAI
 * Single source of truth for all SEO metadata
 */

export const seoConfig = {
  // Basic site information
  siteName: 'DocuChampAI',
  siteUrl: process.env.NEXT_PUBLIC_SITE_URL || 'https://docuchampai.com',

  // Homepage/Default SEO
  defaultTitle: 'DocuChampAI | AI Document Analysis Platform',
  defaultDescription: 'Analyze any images, charts, tables and even handwritings on your documents. Built for professionals who value efficiency over copy-paste workflows.',

  // Global title template for other pages
  titleTemplate: '%s | DocuChampAI',

  // Default keywords (used across the site)
  defaultKeywords: [
    'DocuChampAI',
    'AI document analysis',
    'PDF automation',
    'document processing',
    'text extraction',
    'OCR technology',
    'chart recognition',
    'table extraction',
    'workflow automation',
    'office productivity',
    'document automation',
    'business intelligence',
    'data extraction',
    'report automation',
    'intelligent document processing',
    'multimodal AI',
    'enterprise document solutions',
    'PDF summarization',
    'bulk document processing',
    'automated document review'
  ],

  // Open Graph defaults
  openGraph: {
    type: 'website' as const,
    locale: 'en_US',
    siteName: 'DocuChampAI',
    images: [
      {
        url: '/og-image.webp',
        width: 1200,
        height: 630,
        alt: 'DocuChampAI - AI Document Analysis Platform',
      },
    ],
  },

  // Twitter defaults
  twitter: {
    handle: '@DocuChampAI',
    site: '@DocuChampAI',
    cardType: 'summary_large_image' as const,
  },
  
  // Additional meta tags for enterprise credibility
  additionalMetaTags: [
    {
      name: 'viewport',
      content: 'width=device-width, initial-scale=1, maximum-scale=5',
    },
    {
      name: 'apple-mobile-web-app-capable',
      content: 'yes',
    },
    {
      name: 'apple-mobile-web-app-status-bar-style',
      content: 'default',
    },
    {
      name: 'apple-mobile-web-app-title',
      content: 'DocuChampAI',
    },
    {
      name: 'application-name',
      content: 'DocuChampAI',
    },
    {
      name: 'msapplication-TileColor',
      content: '#8b5cf6',
    },
    {
      name: 'msapplication-config',
      content: '/browserconfig.xml',
    },
    {
      name: 'theme-color',
      content: '#8b5cf6',
    },
    {
      name: 'mobile-web-app-capable',
      content: 'yes',
    },
    {
      name: 'apple-mobile-web-app-orientations',
      content: 'portrait-any',
    },
    {
      name: 'format-detection',
      content: 'telephone=no',
    },
    {
      name: 'HandheldFriendly',
      content: 'true',
    },
    {
      name: 'MobileOptimized',
      content: '320',
    },
    {
      name: 'referrer',
      content: 'no-referrer-when-downgrade',
    },
  ],
  
  // Additional link tags
  additionalLinkTags: [
    {
      rel: 'icon',
      href: '/favicon.ico',
    },
    {
      rel: 'apple-touch-icon',
      href: '/apple-touch-icon.webp',
      sizes: '180x180',
    },
    {
      rel: 'icon',
      type: 'image/webp',
      href: '/favicon-32x32.webp',
      sizes: '32x32',
    },
    {
      rel: 'icon',
      type: 'image/webp',
      href: '/favicon-16x16.webp',
      sizes: '16x16',
    },
    {
      rel: 'manifest',
      href: '/manifest.json',
    },
  ],
}

// Helper function to generate metadata from config
export function generateMetadata(pageConfig?: {
  title?: string
  description?: string
  keywords?: string[]
  image?: string
  type?: 'website' | 'article'
}) {
  const title = pageConfig?.title || seoConfig.defaultTitle
  const description = pageConfig?.description || seoConfig.defaultDescription
  const keywords = pageConfig?.keywords || seoConfig.defaultKeywords
  const image = pageConfig?.image || '/og-image.webp'
  const fullImageUrl = `${seoConfig.siteUrl}${image}`

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: pageConfig?.type || 'website',
      images: [
        {
          url: fullImageUrl,
          width: 1200,
          height: 630,
          alt: title,
        }
      ],
      siteName: seoConfig.siteName,
      locale: seoConfig.openGraph.locale,
    },
    twitter: {
      card: seoConfig.twitter.cardType,
      title,
      description,
      images: [fullImageUrl],
      creator: seoConfig.twitter.handle,
      site: seoConfig.twitter.site,
    },
  }
}

// Note: Page-specific configurations are now handled directly in each page using generateMetadata()

// Enhanced Schema.org structured data for enterprise credibility
export const structuredData = {
  organization: {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'DocuChampAI',
    alternateName: 'DocuChamp AI',
    url: seoConfig.siteUrl,
    logo: `${seoConfig.siteUrl}/og-image.webp`,
    image: `${seoConfig.siteUrl}/og-image.webp`,
    description: seoConfig.defaultDescription,
    foundingDate: '2024',
    slogan: 'Built for professionals who value efficiency over copy-paste workflows',
    knowsAbout: [
      'Artificial Intelligence',
      'Document Processing',
      'Data Extraction',
      'Business Automation',
      'Enterprise Software'
    ],
    sameAs: [
      'https://twitter.com/DocuChampAI',
      process.env.NEXT_PUBLIC_LINKEDIN_URL || 'https://linkedin.com/company/docuchampai',
      process.env.NEXT_PUBLIC_FACEBOOK_URL,
      process.env.NEXT_PUBLIC_INSTAGRAM_URL,
    ].filter(Boolean),
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '******-0123',
      contactType: 'customer service',
      availableLanguage: ['English'],
      areaServed: 'Worldwide',
    },
    address: {
      '@type': 'PostalAddress',
      addressCountry: 'US',
      addressRegion: 'Global',
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.8',
      reviewCount: '150',
      bestRating: '5',
      worstRating: '1',
    },
  },

  // Software Application schema for enterprise software credibility
  softwareApplication: {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: 'DocuChampAI',
    applicationCategory: 'BusinessApplication',
    applicationSubCategory: 'Document Management',
    operatingSystem: 'Web Browser',
    offers: {
      '@type': 'Offer',
      price: '180',
      priceCurrency: 'HKD',
      priceValidUntil: '2025-12-31',
      availability: 'https://schema.org/InStock',
      seller: {
        '@type': 'Organization',
        name: 'DocuChampAI',
      },
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.8',
      reviewCount: '150',
      bestRating: '5',
      worstRating: '1',
    },
    featureList: [
      'AI-powered document analysis',
      'PDF, DOCX, PPTX support',
      'Up to 300 pages processing',
      'Text and table extraction',
      'Automated report generation',
      'Enterprise-grade security',
      'API integration',
      'Bulk processing capabilities'
    ],
    screenshot: `${seoConfig.siteUrl}/screenshot-desktop.webp`,
    softwareVersion: '2.0',
    datePublished: '2024-01-01',
    dateModified: new Date().toISOString().split('T')[0],
    author: {
      '@type': 'Organization',
      name: 'DocuChampAI Team',
    },
    publisher: {
      '@type': 'Organization',
      name: 'DocuChampAI',
      logo: {
        '@type': 'ImageObject',
        url: `${seoConfig.siteUrl}/og-image.webp`,
      },
    },
  },

  website: {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'DocuChampAI',
    url: seoConfig.siteUrl,
    description: seoConfig.defaultDescription,
    potentialAction: {
      '@type': 'SearchAction',
      target: `${seoConfig.siteUrl}/search?q={search_term_string}`,
      'query-input': 'required name=search_term_string',
    },
  },

  // Pricing page structured data
  pricing: {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: 'DocuChampAI',
    description: 'AI-powered document analysis and automation platform',
    brand: {
      '@type': 'Brand',
      name: 'DocuChampAI'
    },
    offers: [
      {
        '@type': 'Offer',
        name: 'Free Plan',
        price: '0',
        priceCurrency: 'HKD',
        description: '10 free credits to get started',
        availability: 'https://schema.org/InStock'
      },
      {
        '@type': 'Offer',
        name: 'Starter',
        price: '180',
        priceCurrency: 'HKD',
        description: '300 credits per month',
        availability: 'https://schema.org/InStock'
      },
      {
        '@type': 'Offer',
        name: 'Professional',
        price: '340',
        priceCurrency: 'HKD',
        description: '600 credits per month',
        availability: 'https://schema.org/InStock'
      },
      {
        '@type': 'Offer',
        name: 'Premium',
        price: '650',
        priceCurrency: 'HKD',
        description: '1200 credits per month',
        availability: 'https://schema.org/InStock'
      }
    ]
  },
}
